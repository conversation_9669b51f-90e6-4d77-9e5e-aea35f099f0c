/* Workout Program Add Specific Styles */

/* Progress Bar Styles */
.progress-container {
  padding: 0 1.5rem 1rem;
  background-color: var(--bg-secondary);
}

.progress-bar {
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-primary);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.progress-step.active .step-circle {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.progress-step.completed .step-circle {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

.step-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.progress-step.active .step-label {
  color: var(--primary);
  font-weight: 600;
}

.progress-step.completed .step-label {
  color: var(--success);
  font-weight: 600;
}

/* Quick Start Options */
.quick-start-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.quick-start-option {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  position: relative;
  overflow: hidden;
}

.quick-start-option:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-start-option.selected {
  border-color: var(--primary);
  background-color: var(--primary-light);
}

.option-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.option-icon.icon-primary {
  background-color: var(--primary-light);
  color: var(--primary);
}

.option-icon.icon-info {
  background-color: var(--info-light);
  color: var(--info);
}

.option-icon.icon-success {
  background-color: var(--success-light);
  color: var(--success);
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.option-description {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.option-arrow {
  color: var(--text-secondary);
  font-size: 1.25rem;
  margin-left: 1rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.quick-start-option:hover .option-arrow {
  color: var(--primary);
  transform: translateX(5px);
}

/* Day Count Options */
.day-count-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.day-count-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  text-align: center;
}

.day-count-option:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.day-count-option.selected {
  border-color: var(--primary);
  background-color: var(--primary-light);
}

.day-count-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.day-count-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.day-count-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.day-count-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

/* Step Content */
.step-content {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.day-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  transition: all var(--transition-speed) var(--transition-timing);
}

.day-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.day-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.day-body {
  padding: 1.25rem;
}

.day-number {
  font-weight: 600;
  color: var(--primary);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--primary-light);
}

.form-check-label {
  font-weight: 500;
  margin-left: 0.5rem;
}

.is-invalid {
  border-color: var(--danger);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 0;
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.alert-info {
  color: var(--info);
  background-color: var(--info-light);
  border-color: rgba(var(--info-rgb), 0.2);
}

.alert-warning {
  color: var(--warning);
  background-color: var(--warning-light);
  border-color: rgba(var(--warning-rgb), 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .progress-container {
    padding: 0 1rem 1rem;
  }

  .progress-steps {
    gap: 0.5rem;
  }

  .step-circle {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }

  .step-label {
    font-size: 0.75rem;
  }

  .quick-start-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quick-start-option {
    padding: 1rem;
  }

  .option-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .option-title {
    font-size: 1rem;
  }

  .day-count-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .day-count-option {
    padding: 1rem 0.75rem;
  }

  .day-count-icon {
    font-size: 1.5rem;
  }

  .day-count-number {
    font-size: 1.25rem;
  }

  .day-count-description {
    font-size: 0.8rem;
  }

  .day-header {
    padding: 0.75rem 1rem;
  }

  .day-body {
    padding: 1rem;
  }

  .modern-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
}

@media (max-width: 480px) {
  .day-count-grid {
    grid-template-columns: 1fr;
  }

  .quick-start-option {
    flex-direction: column;
    text-align: center;
  }

  .option-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .option-arrow {
    margin-left: 0;
    margin-top: 1rem;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .day-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .day-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .form-check-input {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

[data-theme="dark"] .alert-info {
  background-color: var(--info-light);
  border-color: rgba(var(--info-rgb), 0.3);
  color: var(--info);
}

[data-theme="dark"] .alert-warning {
  background-color: var(--warning-light);
  border-color: rgba(var(--warning-rgb), 0.3);
  color: var(--warning);
}
