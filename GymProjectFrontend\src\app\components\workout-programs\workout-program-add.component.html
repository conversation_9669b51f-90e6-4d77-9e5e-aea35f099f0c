<div class="container-fluid">
  <!-- Header with Progress -->
  <div class="modern-card mb-4">
    <div class="modern-card-header">
      <div class="d-flex align-items-center">
        <button
          class="modern-btn modern-btn-outline-secondary me-3"
          (click)="goBack()">
          <fa-icon [icon]="faArrowLeft"></fa-icon>
        </button>
        <div>
          <h4 class="mb-0"><PERSON><PERSON></h4>
          <small class="text-muted">{{getStepTitle()}} ({{currentStep}}/{{totalSteps}})</small>
        </div>
      </div>
      <div class="d-flex gap-2">
        <button
          type="button"
          class="modern-btn modern-btn-secondary"
          (click)="goBack()">
          İptal
        </button>
        <button
          *ngIf="currentStep === totalSteps"
          type="submit"
          form="programForm"
          class="modern-btn modern-btn-primary"
          [disabled]="isSubmitting">
          <fa-icon [icon]="faSave" class="modern-btn-icon"></fa-icon>
          <span *ngIf="!isSubmitting">Kaydet</span>
          <span *ngIf="isSubmitting">Kaydediliyor...</span>
        </button>
        <button
          *ngIf="currentStep < totalSteps && currentStep > 1"
          type="button"
          class="modern-btn modern-btn-primary"
          [disabled]="!isStepCompleted(currentStep)"
          (click)="nextStep()">
          <span>Devam Et</span>
          <fa-icon [icon]="faStepForward" class="ms-2"></fa-icon>
        </button>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
      </div>
      <div class="progress-steps">
        <div *ngFor="let step of [1,2,3]; let i = index"
             class="progress-step"
             [class.completed]="isStepCompleted(step)"
             [class.active]="currentStep === step"
             (click)="goToStep(step)">
          <div class="step-circle">
            <fa-icon *ngIf="isStepCompleted(step)" [icon]="faRocket" class="step-icon"></fa-icon>
            <span *ngIf="!isStepCompleted(step)">{{step}}</span>
          </div>
          <span class="step-label">
            {{step === 1 ? 'Başlangıç' : step === 2 ? 'Bilgiler' : 'Planlama'}}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 1: Quick Start Options -->
  <div *ngIf="currentStep === 1" class="step-content">
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faRocket" class="me-2"></fa-icon>
          Nasıl başlamak istiyorsunuz?
        </h5>
      </div>
      <div class="modern-card-body">
        <div class="quick-start-grid">
          <div *ngFor="let option of quickStartOptions"
               class="quick-start-option"
               [class.selected]="selectedQuickStartOption === option.id"
               (click)="selectQuickStartOption(option.id)">

            <div class="option-icon" [ngClass]="'icon-' + option.color">
              <fa-icon [icon]="option.icon === 'plus-circle' ? faPlusCircle :
                              option.icon === 'copy' ? faCopy :
                              option.icon === 'user' ? faUser : faPlus"></fa-icon>
            </div>

            <div class="option-content">
              <h6 class="option-title">{{option.title}}</h6>
              <p class="option-description">{{option.description}}</p>
            </div>

            <div class="option-arrow">
              <fa-icon [icon]="faStepForward"></fa-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 2: Program Basic Info -->
  <div *ngIf="currentStep === 2" class="step-content">
    <form id="programForm" [formGroup]="programForm" (ngSubmit)="onSubmit()">
      <!-- Program Basic Info -->
      <div class="modern-card mb-4">
        <div class="modern-card-header">
          <h5 class="mb-0">Program Bilgileri</h5>
        </div>
        <div class="modern-card-body">
          <div class="row g-3">
            <!-- Program Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Program Adı <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('programName')"
                  formControlName="programName"
                  placeholder="Örn: Başlangıç Kas Yapma Programı">
                <div *ngIf="isFieldInvalid('programName')" class="invalid-feedback">
                  {{getFieldError('programName')}}
                </div>
              </div>
            </div>

            <!-- Experience Level -->
            <div class="col-md-3">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Deneyim Seviyesi <span class="text-danger">*</span>
                </label>
                <select
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('experienceLevel')"
                  formControlName="experienceLevel">
                  <option value="">Seçiniz</option>
                  <option *ngFor="let level of experienceLevels" [value]="level.value">
                    {{level.label}}
                  </option>
                </select>
                <div *ngIf="isFieldInvalid('experienceLevel')" class="invalid-feedback">
                  {{getFieldError('experienceLevel')}}
                </div>
              </div>
            </div>

            <!-- Target Goal -->
            <div class="col-md-3">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Hedef <span class="text-danger">*</span>
                </label>
                <select
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('targetGoal')"
                  formControlName="targetGoal">
                  <option value="">Seçiniz</option>
                  <option *ngFor="let goal of targetGoals" [value]="goal.value">
                    {{goal.label}}
                  </option>
                </select>
                <div *ngIf="isFieldInvalid('targetGoal')" class="invalid-feedback">
                  {{getFieldError('targetGoal')}}
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="col-12">
              <div class="modern-form-group">
                <label class="modern-form-label">Açıklama (Opsiyonel)</label>
                <textarea
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('description')"
                  formControlName="description"
                  rows="3"
                  placeholder="Program hakkında detaylı bilgi..."></textarea>
                <div *ngIf="isFieldInvalid('description')" class="invalid-feedback">
                  {{getFieldError('description')}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Day Count Selection -->
      <div class="modern-card mb-4">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
            Kaç günlük program oluşturmak istiyorsunuz?
          </h5>
        </div>
        <div class="modern-card-body">
          <div class="day-count-grid">
            <div *ngFor="let option of dayCountOptions"
                 class="day-count-option"
                 [class.selected]="selectedDayCount === option.count"
                 (click)="selectDayCount(option.count)">

              <div class="day-count-icon">{{option.icon}}</div>
              <div class="day-count-number">{{option.count}}</div>
              <div class="day-count-label">{{option.label}}</div>
              <div class="day-count-description">{{option.description}}</div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
      <!-- Program Basic Info -->
      <div class="modern-card mb-4">
        <div class="modern-card-header">
          <h5 class="mb-0">Program Bilgileri</h5>
        </div>
        <div class="modern-card-body">
          <div class="row g-3">
            <!-- Program Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Program Adı <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('programName')"
                  formControlName="programName"
                  placeholder="Örn: Başlangıç Kas Yapma Programı">
                <div *ngIf="isFieldInvalid('programName')" class="invalid-feedback">
                  {{getFieldError('programName')}}
                </div>
              </div>
            </div>

            <!-- Experience Level -->
            <div class="col-md-3">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Deneyim Seviyesi <span class="text-danger">*</span>
                </label>
                <select
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('experienceLevel')"
                  formControlName="experienceLevel">
                  <option value="">Seçiniz</option>
                  <option *ngFor="let level of experienceLevels" [value]="level.value">
                    {{level.label}}
                  </option>
                </select>
                <div *ngIf="isFieldInvalid('experienceLevel')" class="invalid-feedback">
                  {{getFieldError('experienceLevel')}}
                </div>
              </div>
            </div>

            <!-- Target Goal -->
            <div class="col-md-3">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Hedef <span class="text-danger">*</span>
                </label>
                <select
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('targetGoal')"
                  formControlName="targetGoal">
                  <option value="">Seçiniz</option>
                  <option *ngFor="let goal of targetGoals" [value]="goal.value">
                    {{goal.label}}
                  </option>
                </select>
                <div *ngIf="isFieldInvalid('targetGoal')" class="invalid-feedback">
                  {{getFieldError('targetGoal')}}
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="col-12">
              <div class="modern-form-group">
                <label class="modern-form-label">Açıklama (Opsiyonel)</label>
                <textarea
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('description')"
                  formControlName="description"
                  rows="3"
                  placeholder="Program hakkında detaylı bilgi..."></textarea>
                <div *ngIf="isFieldInvalid('description')" class="invalid-feedback">
                  {{getFieldError('description')}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

  </div>

  <!-- Step 3: Day Planning -->
  <div *ngIf="currentStep === 3" class="step-content">
    <form id="programForm" [formGroup]="programForm" (ngSubmit)="onSubmit()">
      <!-- Program Days -->
      <div class="modern-card mb-4">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
            Program Günleri ({{selectedDayCount}} Gün)
          </h5>
        </div>
        <div class="modern-card-body">
          <div formArrayName="days">
            <div
              *ngFor="let dayControl of days.controls; let i = index"
              class="day-card mb-3"
              [formGroupName]="i">

              <div class="day-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">
                    <span class="day-number">{{i + 1}}. Gün</span>
                  </h6>
                  <div class="d-flex gap-2">
                    <button
                      *ngIf="!dayControl.get('isRestDay')?.value"
                      type="button"
                      class="modern-btn modern-btn-outline-primary modern-btn-sm"
                      (click)="openDayEditModal(i)">
                      <fa-icon [icon]="faEdit"></fa-icon>
                    </button>
                  </div>
                </div>
              </div>

              <div class="day-body">
                <div class="row g-3">
                  <!-- Day Name -->
                  <div class="col-md-6">
                    <div class="modern-form-group">
                      <label class="modern-form-label">
                        Gün Adı <span class="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        class="modern-form-control"
                        [class.is-invalid]="isDayFieldInvalid(i, 'dayName')"
                        formControlName="dayName"
                        placeholder="Örn: Göğüs-Triceps">
                      <div *ngIf="isDayFieldInvalid(i, 'dayName')" class="invalid-feedback">
                        {{getDayFieldError(i, 'dayName')}}
                      </div>
                    </div>
                  </div>

                  <!-- Popular Day Names -->
                  <div class="col-md-4">
                    <div class="modern-form-group">
                      <label class="modern-form-label">Popüler Gün Adları</label>
                      <select
                        class="modern-form-control"
                        (change)="onPopularDayNameSelect(i, $any($event.target).value)">
                        <option value="">Seçiniz</option>
                        <option *ngFor="let dayName of popularDayNames" [value]="dayName">
                          {{dayName}}
                        </option>
                      </select>
                    </div>
                  </div>

                  <!-- Rest Day -->
                  <div class="col-md-2">
                    <div class="modern-form-group">
                      <label class="modern-form-label">Dinlenme Günü</label>
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          formControlName="isRestDay"
                          (change)="onRestDayChange(i)"
                          [id]="'restDay' + i">
                        <label class="form-check-label" [for]="'restDay' + i">
                          {{dayControl.get('isRestDay')?.value ? 'Evet' : 'Hayır'}}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Exercise Info -->
                <div *ngIf="!dayControl.get('isRestDay')?.value" class="mt-3">
                  <div class="alert alert-info">
                    <small>
                      <fa-icon [icon]="faEdit" class="me-1"></fa-icon>
                      Egzersizleri eklemek için "Düzenle" butonuna tıklayın
                    </small>
                  </div>
                </div>

                <div *ngIf="dayControl.get('isRestDay')?.value" class="mt-3">
                  <div class="alert alert-warning">
                    <small>
                      Bu gün dinlenme günü olarak işaretlenmiştir
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div *ngIf="days.length === 0" class="text-center py-4">
            <fa-icon [icon]="faCalendarAlt" class="text-muted mb-3" style="font-size: 2rem;"></fa-icon>
            <h6 class="text-muted">Program günleri yükleniyor...</h6>
          </div>
        </div>
      </div>
    </form>
  </div>
