import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { faPlus, faMinus, faEdit, faSave, faArrowLeft, faCalendarAlt, faCopy, faUser, faPlusCircle, faRocket, faStepForward } from '@fortawesome/free-solid-svg-icons';

import { WorkoutProgramService } from '../../services/workout-program.service';
import { WorkoutProgramDayModalComponent } from './workout-program-day-modal.component';
import {
  WorkoutProgramTemplateAdd,
  WorkoutProgramDayAdd,
  POPULAR_DAY_NAMES,
  EXPERIENCE_LEVELS,
  TARGET_GOALS
} from '../../models/workout-program.models';

@Component({
  selector: 'app-workout-program-add',
  templateUrl: './workout-program-add.component.html',
  styleUrls: ['./workout-program-add.component.css'],
  standalone: false
})
export class WorkoutProgramAddComponent implements OnInit {
  // Icons
  faPlus = faPlus;
  faMinus = faMinus;
  faEdit = faEdit;
  faSave = faSave;
  faArrowLeft = faArrowLeft;
  faCalendarAlt = faCalendarAlt;
  faCopy = faCopy;
  faUser = faUser;

  faPlusCircle = faPlusCircle;
  faRocket = faRocket;
  faStepForward = faStepForward;

  // Form
  programForm!: FormGroup;
  isSubmitting = false;

  // UI State
  currentStep = 1;
  totalSteps = 2;
  selectedDayCount = 7;

  // Constants
  popularDayNames = POPULAR_DAY_NAMES;
  experienceLevels = EXPERIENCE_LEVELS;
  targetGoals = TARGET_GOALS;



  // Day count options
  dayCountOptions = [
    {
      count: 3,
      label: '3 Gün',
      description: 'Başlangıç seviyesi veya zaman kısıtı olan üyeler için',
      icon: '🏃‍♂️'
    },
    {
      count: 4,
      label: '4 Gün',
      description: 'Orta seviye antrenman sıklığı',
      icon: '💪'
    },
    {
      count: 5,
      label: '5 Gün',
      description: 'İleri seviye, yoğun antrenman',
      icon: '🏋️‍♂️'
    },
    {
      count: 6,
      label: '6 Gün',
      description: 'Maksimum antrenman sıklığı',
      icon: '🔥'
    },
    {
      count: 7,
      label: '7 Gün',
      description: 'Her gün antrenman (dinlenme günleri dahil)',
      icon: '⚡'
    }
  ];

  constructor(
    private fb: FormBuilder,
    private workoutProgramService: WorkoutProgramService,
    private router: Router,
    private toastrService: ToastrService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.initializeSevenDays(); // Varsayılan olarak 7 gün
  }

  initializeForm(): void {
    this.programForm = this.fb.group({
      programName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]],
      experienceLevel: ['', [Validators.required]],
      targetGoal: ['', [Validators.required]],
      days: this.fb.array([])
    });
  }

  get days(): FormArray {
    return this.programForm.get('days') as FormArray;
  }

  initializeSevenDays(): void {
    // 7 günü başlangıçta ekle
    for (let i = 1; i <= 7; i++) {
      const dayGroup = this.createDayFormGroup(i);
      this.days.push(dayGroup);
    }
  }

  selectDayCount(count: number): void {
    this.selectedDayCount = count;
    this.initializeDaysForCount(count);
  }

  initializeDaysForCount(count: number): void {
    // Mevcut günleri temizle
    while (this.days.length > 0) {
      this.days.removeAt(0);
    }

    // Seçilen sayıda gün oluştur
    for (let i = 1; i <= count; i++) {
      const dayGroup = this.createDayFormGroup(i);
      this.days.push(dayGroup);
    }
  }

  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      if (this.currentStep === 1 && this.selectedDayCount > 0) {
        // 1. adımdan 2. adıma geçerken günleri oluştur
        this.initializeDaysForCount(this.selectedDayCount);
      }
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  goToStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.currentStep = step;
    }
  }

  getStepTitle(): string {
    switch (this.currentStep) {
      case 1: return 'Program Bilgileri';
      case 2: return 'Günlük Planlama';
      default: return 'Program Oluşturma';
    }
  }

  isStepCompleted(step: number): boolean {
    switch (step) {
      case 1: return !!(this.programForm.get('programName')?.valid &&
                       this.programForm.get('experienceLevel')?.valid &&
                       this.programForm.get('targetGoal')?.valid &&
                       this.selectedDayCount > 0);
      case 2: return this.days.length > 0 && this.days.controls.some(day =>
                     !!day.get('dayName')?.valid);
      default: return false;
    }
  }

  createDayFormGroup(dayNumber: number = 1): FormGroup {
    return this.fb.group({
      dayNumber: [dayNumber, [Validators.required, Validators.min(1), Validators.max(7)]],
      dayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      isRestDay: [false],
      exercises: this.fb.array([])
    });
  }

  addDay(): void {
    const dayNumber = this.days.length + 1;
    if (dayNumber <= 7) {
      const dayGroup = this.createDayFormGroup(dayNumber);
      this.days.push(dayGroup);
    } else {
      this.toastrService.warning('Maksimum 7 gün ekleyebilirsiniz', 'Uyarı');
    }
  }

  removeDay(index: number): void {
    if (this.days.length > 1) {
      this.days.removeAt(index);
      // Gün numaralarını yeniden düzenle
      this.updateDayNumbers();
    } else {
      this.toastrService.warning('En az bir gün olmalıdır', 'Uyarı');
    }
  }

  updateDayNumbers(): void {
    this.days.controls.forEach((dayControl, index) => {
      dayControl.get('dayNumber')?.setValue(index + 1);
    });
  }

  onPopularDayNameSelect(dayIndex: number, dayName: string): void {
    const dayControl = this.days.at(dayIndex);
    dayControl.get('dayName')?.setValue(dayName);

    // Eğer "Dinlenme Günü" seçildiyse, dinlenme günü switch'ini aktif et
    if (dayName === 'Dinlenme Günü') {
      dayControl.get('isRestDay')?.setValue(true);
      // Egzersizleri temizle
      const exercisesArray = dayControl.get('exercises') as FormArray;
      exercisesArray.clear();
    }
  }

  onRestDayChange(dayIndex: number): void {
    const dayControl = this.days.at(dayIndex);
    const isRestDay = dayControl.get('isRestDay')?.value;

    if (isRestDay) {
      // Dinlenme günü ise egzersizleri temizle
      const exercisesArray = dayControl.get('exercises') as FormArray;
      exercisesArray.clear();

      // Gün adını "Dinlenme Günü" olarak ayarla
      dayControl.get('dayName')?.setValue('Dinlenme Günü');
    } else {
      // Dinlenme günü kapatıldıysa ve gün adı "Dinlenme Günü" ise temizle
      const currentDayName = dayControl.get('dayName')?.value;
      if (currentDayName === 'Dinlenme Günü') {
        dayControl.get('dayName')?.setValue('');
      }
    }
  }

  openDayEditModal(dayIndex: number): void {
    const dayControl = this.days.at(dayIndex);
    const dayData = dayControl.value;

    const dialogRef = this.dialog.open(WorkoutProgramDayModalComponent, {
      width: '1000px',
      maxWidth: '95vw',
      height: '80vh',
      maxHeight: '80vh',
      data: {
        day: dayData,
        dayNumber: dayData.dayNumber,
        mode: 'edit'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Sadece egzersizleri güncelle
        const exercisesArray = dayControl.get('exercises') as FormArray;
        exercisesArray.clear();

        if (result.exercises && result.exercises.length > 0) {
          result.exercises.forEach((exercise: any) => {
            const exerciseGroup = this.fb.group({
              exerciseType: [exercise.exerciseType],
              exerciseID: [exercise.exerciseID],
              exerciseName: [exercise.exerciseName],
              orderIndex: [exercise.orderIndex],
              sets: [exercise.sets],
              reps: [exercise.reps],
              notes: [exercise.notes]
            });
            exercisesArray.push(exerciseGroup);
          });
        }
      }
    });
  }

  onSubmit(): void {
    if (this.programForm.valid) {
      this.isSubmitting = true;

      // Form verilerini DTO formatına çevir
      const formValue = this.programForm.value;
      const programData: WorkoutProgramTemplateAdd = {
        programName: formValue.programName,
        description: formValue.description,
        experienceLevel: formValue.experienceLevel,
        targetGoal: formValue.targetGoal,
        days: formValue.days.map((day: any) => ({
          dayNumber: day.dayNumber,
          dayName: day.dayName,
          isRestDay: day.isRestDay,
          exercises: day.exercises || []
        }))
      };

      this.workoutProgramService.add(programData).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Antrenman programı başarıyla oluşturuldu', 'Başarılı');
            this.router.navigate(['/workout-programs']);
          } else {
            this.toastrService.error(response.message || 'Program oluşturulurken hata oluştu', 'Hata');
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error creating workout program:', error);
          this.toastrService.error('Program oluşturulurken hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    } else {
      this.markFormGroupTouched(this.programForm);
      this.toastrService.warning('Lütfen tüm gerekli alanları doldurun', 'Uyarı');
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/workout-programs']);
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.programForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isDayFieldInvalid(dayIndex: number, fieldName: string): boolean {
    const dayControl = this.days.at(dayIndex);
    const field = dayControl.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.programForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
    }
    return '';
  }

  getDayFieldError(dayIndex: number, fieldName: string): string {
    const dayControl = this.days.at(dayIndex);
    const field = dayControl.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['min']) return `En az ${field.errors['min'].min} olmalıdır`;
      if (field.errors['max']) return `En fazla ${field.errors['max'].max} olmalıdır`;
    }
    return '';
  }
}
